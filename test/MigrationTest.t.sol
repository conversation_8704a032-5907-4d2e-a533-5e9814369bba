// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import "forge-std/console.sol";
import "../src/DistributionPool.sol";
import "../src/IPCoin.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

// bnb mainnet
contract MigrationTest is Test {
    DistributionPool public distributionPool;
    IERC20 public usdtToken;

    address public admin = address(0x1);
    address public user = address(0x2);
    address public feeReceiver = address(0x3);

    address constant USDT_ADDRESS = 0x55d398326f99059fF775485246999027B3197955;
    address constant UNISWAP_ROUTER = 0x10ED43C718714eb63d5aA57B78B54704E256024E;

    struct PreMigrationState {
        uint256 totalPlatformFee;
        uint256 currentVersion;
        uint256 contractNativeBalance;
        uint256 contractUsdtBalance;
        mapping(address => CoinState) coinStates;
    }

    struct CoinState {
        uint256 usdtAmountInPool;
        uint256 poolVersion;
        bool migrated;
        uint256 minimumUsdtToBuy;
        uint256 liquidityUsdtAmount;
        uint256 virtualUsdtSupply;
        uint256 priceConstant;
    }

    PreMigrationState preMigrationState;
    address[] testCoins;

    function setUp() public {
        // Fork BSC Mainnet
        // vm.createFork("https://hardworking-dark-asphalt.bsc.quiknode.pro/6b3aadc8a13d1de23183ead56b2062da987aa6f9");

        // Get existing DistributionPool contract address (you need to replace with actual deployed address)
        distributionPool = DistributionPool(payable(0x60418FFBF813AE51c11c0E72D9a786B5e9601DaC));

        usdtToken = IERC20(USDT_ADDRESS);

        vm.startPrank(admin);
        // Setup test environment
        _setupTestEnvironment();
        vm.stopPrank();
    }

    function _setupTestEnvironment() internal {
        // Record pre-migration state
        _recordPreMigrationState();
    }

    function _recordPreMigrationState() internal {
        preMigrationState.totalPlatformFee = distributionPool.totalPlatformFee();
        preMigrationState.currentVersion = distributionPool.currentVersion();
        preMigrationState.contractNativeBalance = address(distributionPool).balance;
        preMigrationState.contractUsdtBalance = usdtToken.balanceOf(address(distributionPool));

        console.log("=== Pre-Migration State ===");
        console.log("Total Platform Fee:", preMigrationState.totalPlatformFee);
        console.log("Current Version:", preMigrationState.currentVersion);
        console.log("Contract Native Balance:", preMigrationState.contractNativeBalance);
        console.log("Contract USDT Balance:", preMigrationState.contractUsdtBalance);
    }

    function testMigrateNewVersion() public {
        vm.startPrank(admin);

        console.log("=== Testing migrateNewVersion ===");

        // Record state before migration
        uint256 preNativeBalance = address(distributionPool).balance;
        uint256 preUsdtBalance = usdtToken.balanceOf(address(distributionPool));
        uint256 preTotalPlatformFee = distributionPool.totalPlatformFee();
        uint256 preVersion = distributionPool.currentVersion();

        // Execute migration
        distributionPool.migrateNewVersion();

        // Verify post-migration state
        uint256 postNativeBalance = address(distributionPool).balance;
        uint256 postUsdtBalance = usdtToken.balanceOf(address(distributionPool));
        uint256 postTotalPlatformFee = distributionPool.totalPlatformFee();
        uint256 postVersion = distributionPool.currentVersion();

        console.log("Pre-migration native balance:", preNativeBalance);
        console.log("Post-migration native balance:", postNativeBalance);
        console.log("Pre-migration USDT balance:", preUsdtBalance);
        console.log("Post-migration USDT balance:", postUsdtBalance);
        console.log("Pre-migration totalPlatformFee:", preTotalPlatformFee);
        console.log("Post-migration totalPlatformFee:", postTotalPlatformFee);

        // Assertions
        assertEq(postVersion, 2_025_081_901, "Version should be updated");

        if (preTotalPlatformFee > 0) {
            assertLt(postNativeBalance, preNativeBalance, "Native balance should decrease");
            assertGt(postUsdtBalance, preUsdtBalance, "USDT balance should increase");
            assertGt(postTotalPlatformFee, 0, "Total platform fee should be in USDT");
        }

        vm.stopPrank();
    }

    function testMigrateCoinToUSDT() public {
        // This test requires existing coins in the contract
        // You'll need to add actual coin addresses from your deployment

        vm.startPrank(admin);

        console.log("=== Testing migrateCoinToUSDT ===");

        // For testing, we'll need to get actual coin addresses from the contract
        // address testCoin = GET_ACTUAL_COIN_ADDRESS;

        // Uncomment and modify when you have actual coin addresses:
        /*
        if (testCoin != address(0)) {
            // Record pre-migration state for this coin
            (,,,,,,,uint256 prePoolVersion, uint256 preTokenAmountSold, uint256 preUsdtAmountInPool,,,) =
                distributionPool.coinPoolsData(testCoin);

            bool preMigrated = distributionPool.coinMigratedToUSDT(testCoin);

            console.log("Pre-migration coin USDT amount:", preUsdtAmountInPool);
            console.log("Pre-migration coin version:", prePoolVersion);
            console.log("Pre-migration migrated status:", preMigrated);

            // Execute migration
            distributionPool.migrateCoinToUSDT(testCoin, 0, block.timestamp + 300);

            // Verify post-migration state
            (,,,,,,,uint256 postPoolVersion,, uint256 postUsdtAmountInPool,,,) =
                distributionPool.coinPoolsData(testCoin);

            bool postMigrated = distributionPool.coinMigratedToUSDT(testCoin);

            console.log("Post-migration coin USDT amount:", postUsdtAmountInPool);
            console.log("Post-migration coin version:", postPoolVersion);
            console.log("Post-migration migrated status:", postMigrated);

            // Assertions
            assertEq(postPoolVersion, distributionPool.currentVersion(), "Pool version should be updated");
            assertTrue(postMigrated, "Coin should be marked as migrated");
        }
        */

        vm.stopPrank();
    }

    function testMigrationAccessControl() public {
        console.log("=== Testing Access Control ===");

        // Test that non-admin cannot call migration functions
        vm.startPrank(user);

        vm.expectRevert();
        distributionPool.migrateNewVersion();

        vm.stopPrank();
    }

    function testMigrationIdempotency() public {
        vm.startPrank(admin);

        console.log("=== Testing Migration Idempotency ===");

        // First migration should succeed
        distributionPool.migrateNewVersion();

        // Second migration should revert
        vm.expectRevert("Already migrated to new version");
        distributionPool.migrateNewVersion();

        vm.stopPrank();
    }

    function testSwapFunctionality() public {
        vm.startPrank(admin);

        console.log("=== Testing Swap Functionality ===");

        // Add some ETH to contract for testing swap
        vm.deal(address(distributionPool), 1 ether);

        uint256 preUsdtBalance = usdtToken.balanceOf(address(distributionPool));

        // This test would require the _swapNativeToUSDT function to be public or
        // we need to test it through the migration functions

        // Test through migrateNewVersion if there's platform fee
        if (distributionPool.totalPlatformFee() > 0) {
            distributionPool.migrateNewVersion();

            uint256 postUsdtBalance = usdtToken.balanceOf(address(distributionPool));
            assertGt(postUsdtBalance, preUsdtBalance, "USDT balance should increase after swap");
        }

        vm.stopPrank();
    }

    function testParameterUpdates() public {
        vm.startPrank(admin);

        console.log("=== Testing Parameter Updates ===");

        // Record pre-migration parameters (only public ones)
        uint256 preBondingCurveTradeCap = distributionPool.bondingCurveTradeCap();
        uint256 preMinimumUsdtToBuy = distributionPool.minimumUsdtToBuy();
        uint256 preCurrentVersion = distributionPool.currentVersion();

        console.log("Pre-migration parameters:");
        console.log("- Bonding curve trade cap:", preBondingCurveTradeCap);
        console.log("- Minimum USDT to buy:", preMinimumUsdtToBuy);
        console.log("- Current version:", preCurrentVersion);

        // Execute migration
        distributionPool.migrateNewVersion();

        // Check post-migration parameters
        uint256 postBondingCurveTradeCap = distributionPool.bondingCurveTradeCap();
        uint256 postMinimumUsdtToBuy = distributionPool.minimumUsdtToBuy();
        uint256 postCurrentVersion = distributionPool.currentVersion();

        console.log("Post-migration parameters:");
        console.log("- Bonding curve trade cap:", postBondingCurveTradeCap);
        console.log("- Minimum USDT to buy:", postMinimumUsdtToBuy);
        console.log("- Current version:", postCurrentVersion);

        // Verify expected values from _setCommonParameters()
        assertEq(postBondingCurveTradeCap, 400_000_000 * 1e18, "Bonding curve trade cap should be updated");
        assertEq(postMinimumUsdtToBuy, 1 * 1e3, "Minimum USDT to buy should be updated");
        assertEq(postCurrentVersion, 2_025_081_901, "Current version should be updated");

        vm.stopPrank();
    }
}
