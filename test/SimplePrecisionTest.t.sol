// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import "../src/DistributionPool.sol";
import "@openzeppelin/contracts/token/ERC20/ERC20.sol";

// Mock USDT contract with 6 decimals
contract MockUSDT is ERC20 {
    constructor() ERC20("Mock USDT", "USDT") {}

    function decimals() public pure override returns (uint8) {
        return 6;
    }

    function mint(address to, uint256 amount) external {
        _mint(to, amount);
    }
}

contract SimplePrecisionTest is Test {
    function testPrecisionCalculations() public {
        // Test precision calculations directly without deployment

        // Simulate USDT amounts (6 decimals)
        uint256 usdtAmount = 100 * 1e6; // 100 USDT
        uint256 smallUsdtAmount = 1_000_001; // 1.000001 USDT

        // Simulate token amounts (18 decimals)
        uint256 virtualTokenSupply = 106_774_000 * 1e18;
        uint256 tokenAmountInPool = 50_000_000 * 1e18;

        // Simulate USDT reserves (6 decimals)
        uint256 virtualUsdtSupply = 466 * 1e6;
        uint256 usdtAmountInPool = 10_000 * 1e6;

        // Test buy calculation: mulDiv(usdtAmount, virtualS, virtualU)
        uint256 virtualS = virtualTokenSupply + tokenAmountInPool;
        uint256 virtualU = virtualUsdtSupply + usdtAmountInPool;

        console.log("=== Buy Calculation Test ===");
        console.log("USDT amount (6 decimals):", usdtAmount);
        console.log("Virtual token supply (18 decimals):", virtualS);
        console.log("Virtual USDT supply (6 decimals):", virtualU);

        // Calculate tokens to receive
        uint256 tokensToReceive = (usdtAmount * virtualS) / virtualU;
        console.log("Tokens to receive (18 decimals):", tokensToReceive);

        // Verify precision: result should be in 18 decimals
        assertGt(tokensToReceive, 0, "Should receive some tokens");

        // Test with small amount
        console.log("=== Small Amount Test ===");
        console.log("Small USDT amount:", smallUsdtAmount);
        uint256 smallTokensToReceive = (smallUsdtAmount * virtualS) / virtualU;
        console.log("Small tokens to receive:", smallTokensToReceive);
        assertGt(smallTokensToReceive, 0, "Should receive tokens even for small USDT amount");

        // Test sell calculation: mulDiv(tokenAmount, virtualU, virtualS)
        console.log("=== Sell Calculation Test ===");
        uint256 tokenAmountToSell = 1000 * 1e18; // 1000 tokens
        console.log("Token amount to sell (18 decimals):", tokenAmountToSell);

        uint256 usdtToReceive = (tokenAmountToSell * virtualU) / virtualS;
        console.log("USDT to receive (6 decimals):", usdtToReceive);

        // Verify precision: result should be in 6 decimals
        assertGt(usdtToReceive, 0, "Should receive some USDT");

        // Test fee calculation with mulWad
        console.log("=== Fee Calculation Test ===");
        uint256 tradeFeeRatio = 3 * 1e16; // 3% in 18 decimals
        console.log("Trade fee ratio (18 decimals):", tradeFeeRatio);

        // Fee calculation: (usdtAmount * tradeFeeRatio) / 1e18
        uint256 fee = (usdtAmount * tradeFeeRatio) / 1e18;
        console.log("Fee for 100 USDT (6 decimals):", fee);

        // Should be 3 USDT (3 * 1e6)
        assertEq(fee, 3 * 1e6, "Fee should be 3 USDT");

        // Test minimum amounts
        console.log("=== Minimum Amount Test ===");
        uint256 minimumUsdtToBuy = 1 * 1e6; // 1 USDT minimum
        console.log("Minimum USDT to buy:", minimumUsdtToBuy);

        assertTrue(usdtAmount >= minimumUsdtToBuy, "100 USDT should be >= minimum");
        assertTrue(smallUsdtAmount >= minimumUsdtToBuy, "1.000001 USDT should be >= minimum");

        // Test edge case: very small USDT amount
        uint256 verySmallUsdt = 1; // 0.000001 USDT
        uint256 verySmallTokens = (verySmallUsdt * virtualS) / virtualU;
        console.log("Very small USDT (0.000001):", verySmallUsdt);
        console.log("Very small tokens result:", verySmallTokens);

        // This might be 0 due to precision loss, which is expected for extremely small amounts
        console.log("Very small tokens > 0:", verySmallTokens > 0);
    }

    function testPrecisionEdgeCases() public {
        console.log("=== Edge Cases Test ===");

        // Test with maximum values to check for overflow
        uint256 maxUint256 = type(uint256).max;
        console.log("Max uint256:", maxUint256);

        // Test reasonable large values
        uint256 largeUsdtAmount = 1_000_000 * 1e6; // 1M USDT
        uint256 largeVirtualTokenSupply = 1_000_000_000 * 1e18; // 1B tokens
        uint256 largeVirtualUsdtSupply = 100_000 * 1e6; // 100K USDT

        console.log("Large USDT amount:", largeUsdtAmount);
        console.log("Large virtual token supply:", largeVirtualTokenSupply);
        console.log("Large virtual USDT supply:", largeVirtualUsdtSupply);

        // This should not overflow
        uint256 result = (largeUsdtAmount * largeVirtualTokenSupply) / largeVirtualUsdtSupply;
        console.log("Large calculation result:", result);
        assertGt(result, 0, "Large calculation should work");

        // Test precision loss scenarios
        uint256 smallNumerator = 1;
        uint256 largeDenominator = 1e18;
        uint256 precisionLossResult = smallNumerator / largeDenominator;
        console.log("Precision loss test (1 / 1e18):", precisionLossResult);
        assertEq(precisionLossResult, 0, "Should be 0 due to precision loss");
    }
}
