// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";

/**
 * @title MockUSDT
 * @notice Mock USDT token for testing purposes
 * @dev Uses 18 decimals like BNB chain USDT
 */
contract MockUSDT is ERC20 {
    uint8 private constant DECIMALS = 18;

    constructor() ERC20("Mock USDT", "MUSDT") {
        // Mint initial supply to deployer
        _mint(msg.sender, 1_000_000_000 * 10 ** DECIMALS); // USDT
    }

    function decimals() public pure override returns (uint8) {
        return DECIMALS;
    }

    /**
     * @notice Mint tokens to any address (for testing)
     * @param to Address to mint to
     * @param amount Amount to mint
     */
    function mint(address to, uint256 amount) external {
        _mint(to, amount);
    }

    /**
     * @notice Burn tokens from sender (for testing)
     * @param amount Amount to burn
     */
    function burn(
        uint256 amount
    ) external {
        _burn(msg.sender, amount);
    }
}
