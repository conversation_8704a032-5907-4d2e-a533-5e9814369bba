// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "forge-std/Test.sol";
import "forge-std/console.sol";

// Simple contract to demonstrate USDT vs ETH precision issues
contract PrecisionIssueDemo is Test {
    function testPrecisionProblemDemonstration() public {
        console.log("=== USDT vs ETH Precision Problem Demonstration ===");

        // Constants
        uint256 USDT_DECIMALS = 1e6; // 6 decimals
        uint256 ETH_DECIMALS = 1e18; // 18 decimals
        uint256 WAD = 1e18; // FixedPointMathLib uses 1e18 as WAD

        console.log("USDT Decimals:", USDT_DECIMALS);
        console.log("ETH Decimals:", ETH_DECIMALS);
        console.log("Precision difference factor:", ETH_DECIMALS / USDT_DECIMALS);

        // Test case 1: Fee calculation problems
        console.log("=== Fee Calculation Problem ===");
        uint256 usdtAmount = 1000 * USDT_DECIMALS; // 1000 USDT
        uint256 feeRatio = 1000; // 10% in basis points, but stored as WAD

        console.log("USDT Amount: 1000 USDT =", usdtAmount, "units");
        console.log("Fee Ratio (10%):", feeRatio);

        // Current problematic way (using WAD with USDT)
        uint256 problematicFee = (usdtAmount * feeRatio) / WAD;
        console.log("Fee using WAD (1e18) - WRONG:", problematicFee);

        // Correct way for USDT
        uint256 correctFee = (usdtAmount * feeRatio) / 10_000; // Direct basis points
        console.log("Fee using basis points - CORRECT:", correctFee);
        console.log("Expected fee (10% of 1000 USDT):", usdtAmount / 10);

        console.log("Error factor:", problematicFee == 0 ? type(uint256).max : correctFee / problematicFee);

        // Test case 2: Virtual supply mismatch
        console.log("=== Virtual Supply Precision Mismatch ===");
        uint256 virtualUsdtSupply = 466 * USDT_DECIMALS; // 466 USDT (6 decimals)
        uint256 virtualTokenSupply = 8_000_000 * ETH_DECIMALS; // 8M tokens (18 decimals)

        console.log("Virtual USDT Supply:", virtualUsdtSupply);
        console.log("Virtual Token Supply:", virtualTokenSupply);

        // Bonding curve calculation: tokenAmount = (usdtAmount * virtualTokenSupply) / virtualUsdtSupply
        uint256 testUsdtAmount = 100 * USDT_DECIMALS; // 100 USDT
        uint256 calculatedTokens = (testUsdtAmount * virtualTokenSupply) / virtualUsdtSupply;

        console.log("For 100 USDT, calculated tokens:", calculatedTokens / ETH_DECIMALS, "tokens");
        console.log("Raw calculated tokens:", calculatedTokens);

        // Test case 3: Exchange rate precision in migration
        console.log("=== Migration Exchange Rate Precision ===");
        uint256 ethAmount = 5 * ETH_DECIMALS; // 5 ETH (18 decimals)
        uint256 usdtReceived = 10_000 * USDT_DECIMALS; // 10,000 USDT (6 decimals)

        console.log("ETH Amount:", ethAmount / ETH_DECIMALS, "ETH");
        console.log("USDT Received:", usdtReceived / USDT_DECIMALS, "USDT");

        // Current exchange rate calculation (from migration code)
        uint256 exchangeRate = (usdtReceived * 1e18) / ethAmount;
        console.log("Exchange Rate (scaled by 1e18):", exchangeRate);
        console.log("Actual rate (USDT per ETH):", (usdtReceived / USDT_DECIMALS) / (ethAmount / ETH_DECIMALS));
    }

    function testPrecisionScalingNeeded() public {
        console.log("=== Required Precision Scaling ===");

        uint256 PRECISION_SCALE_FACTOR = 1e12; // 18 - 6 = 12

        console.log("Required scaling factor:", PRECISION_SCALE_FACTOR);

        // Example: How to correctly handle USDT amounts in 18-decimal calculations
        uint256 usdtAmount = 100 * 1e6; // 100 USDT (6 decimals)
        uint256 scaledUsdtAmount = usdtAmount * PRECISION_SCALE_FACTOR; // Scale to 18 decimals

        console.log("Original USDT amount (6 decimals):", usdtAmount);
        console.log("Scaled USDT amount (18 decimals):", scaledUsdtAmount);
        console.log("Scaled back to USDT:", scaledUsdtAmount / PRECISION_SCALE_FACTOR);

        // Verify scaling works correctly
        assertEq(scaledUsdtAmount / PRECISION_SCALE_FACTOR, usdtAmount, "Scaling should be reversible");
    }
}
