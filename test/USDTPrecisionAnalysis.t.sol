// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "forge-std/Test.sol";
import "forge-std/console.sol";
import "../src/DistributionPool.sol";
import "../src/Factory.sol";
import "../src/IPCoin.sol";
import "../src/CreatorNft.sol";
import "../test/mocks/MockUSDT.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
// Remove direct import - we'll use the library from DistributionPool

contract USDTPrecisionAnalysisTest is Test {
    DistributionPool public pool;
    Factory public factory;
    MockUSDT public usdt;
    IPCoin public coin;

    address public admin = address(0x1);
    address public feeReceiver = address(0x2);
    address public user1 = address(0x3);
    address public creator = address(0x4);
    address public aiWallet = address(0x5);

    function setUp() public {
        // Set chain ID to Base Sepolia for testing
        vm.chainId(84_532);

        // Deploy USDT mock (6 decimals)
        usdt = new MockUSDT();

        // Deploy and initialize DistributionPool
        DistributionPool poolImpl = new DistributionPool();
        pool = DistributionPool(
            payable(
                address(
                    new ERC1967Proxy(
                        address(poolImpl),
                        abi.encodeWithSelector(
                            DistributionPool.initialize.selector,
                            admin,
                            feeReceiver,
                            address(0), // Factory will be set later
                            address(usdt)
                        )
                    )
                )
            )
        );

        // Deploy other components
        CreatorNft creatorNftImpl = new CreatorNft();

        Factory factoryImpl = new Factory();
        factory = Factory(
            address(
                new ERC1967Proxy(
                    address(factoryImpl),
                    abi.encodeWithSelector(
                        Factory.initialize.selector,
                        address(pool),
                        address(creatorNftImpl),
                        address(0), // No trader NFT for this test
                        admin
                    )
                )
            )
        );

        // Grant role to factory
        vm.prank(admin);
        pool.grantRole(pool.ADDIP_ROLE(), address(factory));

        // Deploy a test coin
        Factory.DeploymentWithUsdtParams memory params = Factory.DeploymentWithUsdtParams({
            coinName: "Test Coin",
            coinSymbol: "TEST",
            creator: creator,
            aiAgentWallet: aiWallet,
            creatorNftName: "Test Creator NFT",
            creatorNftSymbol: "TCNFT",
            creatorNftURI: "https://example.com/nft",
            ipRef: "test-ip-ref",
            usdtAmount: 0
        });

        vm.prank(admin);
        address coinAddr = factory.deployIP(params);
        coin = IPCoin(coinAddr);

        // Mint USDT to users
        usdt.mint(user1, 100_000 * 1e6); // 100k USDT

        // Approve USDT spending
        vm.prank(user1);
        usdt.approve(address(pool), type(uint256).max);
    }

    function testPrecisionProblems() public {
        console.log("=== USDT Precision Problem Analysis ===");

        // Test 1: Check FixedPointMathLib.mulWad with USDT amounts
        uint256 usdtAmount = 1000 * 1e6; // 1000 USDT (6 decimals)
        uint256 feeRatio = 1000; // 10% = 1000/10000, but stored in WAD format

        console.log("USDT Amount:", usdtAmount);
        console.log("Fee Ratio (basis points):", feeRatio);

        // Test precision issues by checking actual contract behavior
        uint256 tokenAmount = pool.calculateTokenAmount(address(coin), usdtAmount);
        console.log("Token amount for 1000 USDT:", tokenAmount);

        // Test smaller amounts to check precision
        uint256 smallUsdtAmount = 1 * 1e6; // 1 USDT
        uint256 smallTokenAmount = pool.calculateTokenAmount(address(coin), smallUsdtAmount);
        console.log("Token amount for 1 USDT:", smallTokenAmount);

        // Test 2: Check token amount calculation precision
        console.log("=== Token Amount Calculation Analysis ===");

        uint256 testUsdtAmount = 100 * 1e6; // 100 USDT
        console.log("Test USDT Amount:", testUsdtAmount);

        uint256 testTokenAmount = pool.calculateTokenAmount(address(coin), testUsdtAmount);
        console.log("Calculated Token Amount:", testTokenAmount);

        // This should NOT be zero or extremely small due to precision issues
        assertTrue(testTokenAmount > 0, "Token amount should not be zero");

        // Test 3: Check virtual supply values
        console.log("=== Virtual Supply Analysis ===");
        (uint256 capacity, uint256 supply) = pool.getCapAndSupply(address(coin));
        console.log("Pool Capacity (USDT):", capacity);
        console.log("Token Supply:", supply);
    }

    function testFeeCalculationPrecision() public {
        console.log("=== Fee Calculation Precision Test ===");

        uint256[] memory testAmounts = new uint256[](4);
        testAmounts[0] = 1 * 1e6; // 1 USDT
        testAmounts[1] = 10 * 1e6; // 10 USDT
        testAmounts[2] = 100 * 1e6; // 100 USDT
        testAmounts[3] = 1000 * 1e6; // 1000 USDT

        for (uint256 i = 0; i < testAmounts.length; i++) {
            uint256 amount = testAmounts[i];
            uint256 tokenAmount = pool.calculateTokenAmount(address(coin), amount);

            console.log("USDT Amount:", amount / 1e6);
            console.log("Token Amount:", tokenAmount / 1e18);
            console.log("---");
        }
    }

    function testBuyingWithCorrectPrecision() public {
        console.log("=== Buy Function Precision Test ===");

        uint256 usdtAmount = 100 * 1e6; // 100 USDT
        uint256 balanceBefore = usdt.balanceOf(user1);

        console.log("User USDT balance before:", balanceBefore / 1e6);
        console.log("Attempting to buy with:", usdtAmount / 1e6, "USDT");

        // Try to buy tokens
        vm.prank(user1);
        try pool.buy(address(coin), user1, usdtAmount, 0) {
            console.log("Buy transaction successful");

            uint256 balanceAfter = usdt.balanceOf(user1);
            uint256 spent = balanceBefore - balanceAfter;
            console.log("USDT spent:", spent / 1e6);
            console.log("Token balance:", coin.balanceOf(user1) / 1e18);
        } catch Error(string memory reason) {
            console.log("Buy failed with reason:", reason);
        }
    }
}
