// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import {DistributionPool} from "../src/DistributionPool.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract TestWithdrawFees is Script {
    function setUp() public {}

    function run() public {
        // Read configuration from environment variables
        uint256 deployerPrivateKey = vm.envUint("NETWORK_ADMIN_PRIVATE_KEY");
        address poolProxyAddress = vm.envAddress("POOL_PROXY_ADDRESS");

        // Test parameters - modify these as needed
        // address coinAddress = vm.envAddress("COIN_ADDRESS"); // The IP coin address
        // address recipientAddress = vm.envAddress("RECIPIENT_ADDRESS"); // Address to receive withdrawn fees
        // address authorizedWithdrawer = vm.envAddress("AUTHORIZED_WITHDRAWER"); // Address authorized to withdraw IP holders fees

        address coinAddress = 0x90419c015284856CDb4Dec917458c4D80b97F86d; // The IP coin address
        address recipientAddress = 0x6156CD0A43f78A2De0B6b963B9D866B8149983A7; // Address to receive withdrawn fees
        address authorizedWithdrawer = 0x6156CD0A43f78A2De0B6b963B9D866B8149983A7; // Address authorized to withdraw IP holders fees

        console.log("=== Test Withdraw Fees Transaction ===");
        console.log("Pool Proxy Address:", poolProxyAddress);
        console.log("Coin Address:", coinAddress);
        console.log("Recipient Address:", recipientAddress);
        console.log("Authorized Withdrawer:", authorizedWithdrawer);

        vm.startBroadcast(deployerPrivateKey);

        // Get DistributionPool contract instance
        DistributionPool pool = DistributionPool(payable(poolProxyAddress));

        // Step 1: Set authorized IP holders fees withdrawer
        console.log("=== Step 1: Setting Authorized IP Holders Fees Withdrawer ===");
        try pool.setAuthorizedIPHoldersFeesWithdrawer(authorizedWithdrawer) {
            console.log("Successfully set authorized IP holders fees withdrawer:", authorizedWithdrawer);
        } catch Error(string memory reason) {
            console.log("Failed to set authorized withdrawer. Reason:", reason);
        } catch (bytes memory lowLevelData) {
            console.log("Failed to set authorized withdrawer with low level error:");
            console.logBytes(lowLevelData);
        }

        vm.stopBroadcast();

        // Step 2: Test withdraw functions
        console.log("=== Step 2: Testing Withdraw Functions ===");

        // Test 1: withdrawUsdtFees (admin only)
        console.log("\n--- Test 1: withdrawUsdtFees ---");
        vm.startBroadcast(deployerPrivateKey);
        try pool.withdrawUsdtFees() {
            console.log("Successfully withdrew USDT fees");
        } catch Error(string memory reason) {
            console.log("Failed to withdraw USDT fees. Reason:", reason);
        } catch (bytes memory lowLevelData) {
            console.log("Failed to withdraw USDT fees with low level error:");
            console.logBytes(lowLevelData);
        }
        vm.stopBroadcast();

        // Test 3: withdrawRewardPoolFees (admin only)
        console.log("\n--- Test 3: withdrawRewardPoolFees ---");
        vm.startBroadcast(deployerPrivateKey);
        try pool.withdrawRewardPoolFees(coinAddress, recipientAddress) {
            console.log("Successfully withdrew reward pool fees for coin:", coinAddress);
        } catch Error(string memory reason) {
            console.log("Failed to withdraw reward pool fees. Reason:", reason);
        } catch (bytes memory lowLevelData) {
            console.log("Failed to withdraw reward pool fees with low level error:");
            console.logBytes(lowLevelData);
        }
        vm.stopBroadcast();

        // Test 4: withdrawIPHoldersFees (authorized withdrawer only)
        console.log("\n--- Test 4: withdrawIPHoldersFees (using authorized withdrawer) ---");

        // Get the private key for authorized withdrawer
        uint256 authorizedWithdrawerPrivateKey;
        try vm.envUint("AUTHORIZED_WITHDRAWER_PRIVATE_KEY") returns (uint256 key) {
            authorizedWithdrawerPrivateKey = key;
        } catch {
            console.log("AUTHORIZED_WITHDRAWER_PRIVATE_KEY not found, using admin key instead");
            authorizedWithdrawerPrivateKey = deployerPrivateKey;
        }

        vm.startBroadcast(authorizedWithdrawerPrivateKey);
        try pool.withdrawIPHoldersFees(coinAddress, recipientAddress) {
            console.log("Successfully withdrew IP holders fees for coin:", coinAddress);
        } catch Error(string memory reason) {
            console.log("Failed to withdraw IP holders fees. Reason:", reason);
        } catch (bytes memory lowLevelData) {
            console.log("Failed to withdraw IP holders fees with low level error:");
            console.logBytes(lowLevelData);
        }
        vm.stopBroadcast();

        // Display fee distribution status
        console.log("=== Fee Distribution Status ===");
        try pool.getCoinFeeDistribution(coinAddress) returns (DistributionPool.FeeDistribution memory feeData) {
            console.log("IP Holders Unclaimed:", feeData.ipHoldersUnclaimed);
            console.log("Reward Pool Unclaimed:", feeData.rewardPoolUnclaimed);
            console.log("Creator Rewards Total:", feeData.creatorRewardsTotal);
        } catch {
            console.log("Could not retrieve fee distribution data");
        }

        // Display total USDT fees
        try pool.totalPlatformFee() returns (uint256 totalFees) {
            console.log("Total USDT Fees:", totalFees);
        } catch {
            console.log("Could not retrieve total USDT fees");
        }

        console.log("=== Test Completed ===");
    }
}
