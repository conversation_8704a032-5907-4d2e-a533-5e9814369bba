// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import {DistributionPool} from "../src/DistributionPool.sol";

contract TestMigrateNewVersion is Script {
    function setUp() public {}

    function run() public {
        // Read configuration from environment variables
        uint256 deployerPrivateKey = vm.envUint("NETWORK_ADMIN_PRIVATE_KEY");
        address poolProxyAddress = vm.envAddress("POOL_PROXY_ADDRESS");

        console.log("=== Test Migrate New Version Transaction ===");
        console.log("Pool Proxy Address:", poolProxyAddress);

        vm.startBroadcast(deployerPrivateKey);

        // Get DistributionPool contract instance
        DistributionPool pool = DistributionPool(payable(poolProxyAddress));

        // Check current version before migration
        console.log("=== Pre-Migration Status ===");
        try pool.currentVersion() returns (uint256 currentVer) {
            console.log("Current Version:", currentVer);
        } catch {
            console.log("Could not retrieve current version");
        }

        // Check if caller has admin role
        bytes32 adminRole = pool.ADMIN_ROLE();
        address caller = vm.addr(deployerPrivateKey);
        bool hasAdminRole = pool.hasRole(adminRole, caller);
        console.log("Caller Address:", caller);
        console.log("Has Admin Role:", hasAdminRole);

        if (!hasAdminRole) {
            console.log("ERROR: Caller does not have ADMIN_ROLE. Migration will fail.");
            vm.stopBroadcast();
            return;
        }

        // Attempt to migrate to new version
        console.log("=== Executing Migration ===");
        try pool.migrateNewVersion() {
            console.log("Successfully migrated to new version!");

            // Check version after migration
            console.log("=== Post-Migration Status ===");
            try pool.currentVersion() returns (uint256 newVer) {
                console.log("New Version:", newVer);
            } catch {
                console.log("Could not retrieve new version");
            }
        } catch Error(string memory reason) {
            console.log("Migration failed with reason:", reason);

            // Check if already migrated
            if (keccak256(bytes(reason)) == keccak256(bytes("Already migrated to new version"))) {
                console.log("Contract is already at the latest version");

                // Still show current version
                try pool.currentVersion() returns (uint256 currentVer) {
                    console.log("Current Version:", currentVer);
                } catch {
                    console.log("Could not retrieve current version");
                }
            }
        } catch (bytes memory lowLevelData) {
            console.log("Migration failed with low level error:");
            console.logBytes(lowLevelData);
        }

        vm.stopBroadcast();

        console.log("=== Migration Test Completed ===");
    }
}
