// // SPDX-License-Identifier: UNLICENSED
// pragma solidity ^0.8.13;

// import {Script, console} from "forge-std/Script.sol";
// import {Factory} from "../src/Factory.sol";
// import {DistributionPool} from "../src/DistributionPool.sol";
// import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

// /**
//  * @title VerifyMigrationStatus
//  * @notice 验证迁移状态的脚本
//  * @dev 用于检查合约升级和迁移是否成功完成
//  */
// contract VerifyMigrationStatus is Script {
//     // 环境变量配置
//     address private factoryProxyAddress;
//     address private poolProxyAddress;

//     // 合约实例
//     Factory private factory;
//     DistributionPool private distributionPool;

//     function setUp() public {
//         // 从环境变量读取配置
//         factoryProxyAddress = vm.envAddress("FACTORY_PROXY_ADDRESS");
//         poolProxyAddress = vm.envAddress("POOL_PROXY_ADDRESS");

//         // 初始化合约实例
//         factory = Factory(factoryProxyAddress);
//         distributionPool = DistributionPool(payable(poolProxyAddress));

//         console.log("=== Migration Status Verification Script ===");
//         console.log("Factory Proxy Address:", factoryProxyAddress);
//         console.log("Pool Proxy Address:", poolProxyAddress);
//     }

//     function run() public view {
//         console.log("=== 验证迁移状态 ===");

//         // 验证合约基本功能
//         verifyContractAccess();

//         // 验证迁移状态
//         verifyMigrationStatus();

//         // 验证币种迁移状态
//         verifyCoinMigrationStatus();

//         // 验证余额状态
//         verifyBalanceStatus();

//         console.log("=== 验证完成 ===");
//     }

//     /**
//      * @notice 验证合约访问
//      */
//     function verifyContractAccess() internal view {
//         console.log("=== 验证合约访问 ===");

//         // 验证 Factory 合约
//         try factory.owner() returns (address owner) {
//             console.log("✅ Factory 合约可访问");
//             console.log("   所有者:", owner);
//             console.log("   合约地址:", address(factory));
//         } catch Error(string memory reason) {
//             console.log("❌ Factory 合约访问失败:", reason);
//         } catch {
//             console.log("❌ Factory 合约访问失败: 未知错误");
//         }

//         // 验证 DistributionPool 合约
//         try distributionPool.currentVersion() returns (uint256 version) {
//             console.log("✅ DistributionPool 合约可访问");
//             console.log("   当前版本:", version);
//             console.log("   合约地址:", address(distributionPool));
//         } catch Error(string memory reason) {
//             console.log("❌ DistributionPool 合约访问失败:", reason);
//         } catch {
//             console.log("❌ DistributionPool 合约访问失败: 未知错误");
//         }
//     }

//     /**
//      * @notice 验证迁移状态
//      */
//     function verifyMigrationStatus() internal view {
//         console.log("=== 验证迁移状态 ===");

//         try distributionPool.currentVersion() returns (uint256 version) {
//             console.log("当前版本:", version);

//             if (version >= 2_025_081_901) {
//                 console.log("✅ 已迁移到新版本");
//             } else {
//                 console.log("⚠️  尚未迁移到新版本 (期望: 2025081901)");
//             }
//         } catch {
//             console.log("❌ 无法获取版本信息");
//         }

//         // 检查全局参数
//         try distributionPool.bondingCurveTradeCap() returns (uint256 tradeCap) {
//             console.log("债券曲线交易上限:", tradeCap);

//             uint256 expectedTradeCap = 400_000_000 * 1e18;
//             if (tradeCap == expectedTradeCap) {
//                 console.log("✅ 债券曲线交易上限已更新");
//             } else {
//                 console.log("⚠️  债券曲线交易上限未更新 (期望:", expectedTradeCap, ")");
//             }
//         } catch {
//             console.log("❌ 无法获取债券曲线交易上限");
//         }

//         try distributionPool.minimumUsdtToBuy() returns (uint256 minUsdt) {
//             console.log("最小 USDT 购买量:", minUsdt);

//             uint256 expectedMinUsdt = 1 * 1e3; // 0.001 USDT
//             if (minUsdt == expectedMinUsdt) {
//                 console.log("✅ 最小 USDT 购买量已更新");
//             } else {
//                 console.log("⚠️  最小 USDT 购买量未更新 (期望:", expectedMinUsdt, ")");
//             }
//         } catch {
//             console.log("❌ 无法获取最小 USDT 购买量");
//         }
//     }

//     /**
//      * @notice 验证币种迁移状态
//      */
//     function verifyCoinMigrationStatus() internal view {
//         console.log("=== 验证币种迁移状态 ===");

//         // 获取需要检查的币种列表
//         address[] memory coinsToCheck = _getCoinsToCheck();

//         if (coinsToCheck.length == 0) {
//             console.log("ℹ️  没有指定要检查的币种");
//             return;
//         }

//         console.log("检查币种数量:", coinsToCheck.length);

//         for (uint256 i = 0; i < coinsToCheck.length; i++) {
//             _verifySingleCoinMigration(coinsToCheck[i]);
//         }
//     }

//     /**
//      * @notice 获取需要检查的币种列表
//      */
//     function _getCoinsToCheck() internal view returns (address[] memory) {
//         // 尝试从多个环境变量读取币种地址
//         address[] memory tempCoins = new address[](10);
//         uint256 coinCount = 0;

//         // 尝试读取 COIN_TO_MIGRATE_1 到 COIN_TO_MIGRATE_10
//         for (uint256 i = 1; i <= 10; i++) {
//             string memory envVar = string(abi.encodePacked("COIN_TO_MIGRATE_", vm.toString(i)));
//             try vm.envAddress(envVar) returns (address coinAddress) {
//                 if (coinAddress != address(0)) {
//                     tempCoins[coinCount] = coinAddress;
//                     coinCount++;
//                 }
//             } catch {
//                 // 如果环境变量不存在，跳过
//                 break;
//             }
//         }

//         // 创建正确大小的数组
//         address[] memory coins = new address[](coinCount);
//         for (uint256 i = 0; i < coinCount; i++) {
//             coins[i] = tempCoins[i];
//         }

//         return coins;
//     }

//     /**
//      * @notice 验证单个币种的迁移状态
//      */
//     function _verifySingleCoinMigration(
//         address coinAddress
//     ) internal view {
//         console.log("\n--- 检查币种:", coinAddress, "---");

//         // 检查币种是否已迁移
//         try distributionPool.isCoinMigratedToUSDT(coinAddress) returns (bool migrated) {
//             if (migrated) {
//                 console.log("✅ 币种已迁移到 USDT");
//             } else {
//                 console.log("⚠️  币种尚未迁移到 USDT");
//             }
//         } catch {
//             console.log("❌ 无法检查币种迁移状态");
//             return;
//         }

//         // 检查币种池版本
//         try distributionPool.coinPoolsData(coinAddress) returns (
//             address ipCoinContract,
//             address traderNftContract,
//             address creatorNftContract,
//             bool allowTrade,
//             bool tradeNftStatus,
//             address pairAddress,
//             address aiWallet,
//             uint256 poolVersion,
//             uint256 tokenAmountSold,
//             uint256 usdtAmountInPool,
//             uint256 intervalRewardDistributionTotal,
//             uint256 epochCreatorRewardDistributionTotal
//         ) {
//             console.log("   池版本:", poolVersion);
//             console.log("   USDT 池中数量:", usdtAmountInPool);
//             console.log("   代币销售数量:", tokenAmountSold);
//             console.log("   配对地址:", pairAddress);

//             if (poolVersion >= 2_025_081_901) {
//                 console.log("✅ 币种池版本已更新");
//             } else {
//                 console.log("⚠️  币种池版本未更新");
//             }
//         } catch {
//             console.log("❌ 无法获取币种池数据");
//         }
//     }

//     /**
//      * @notice 验证余额状态
//      */
//     function verifyBalanceStatus() internal view {
//         console.log("=== 验证余额状态 ===");

//         // 检查合约原生代币余额
//         uint256 nativeBalance = address(distributionPool).balance;
//         console.log("合约原生代币余额:", nativeBalance);

//         // 检查合约 USDT 余额
//         try distributionPool.usdtToken() returns (IERC20 usdtToken) {
//             if (address(usdtToken) != address(0)) {
//                 uint256 usdtBalance = usdtToken.balanceOf(address(distributionPool));
//                 console.log("合约 USDT 余额:", usdtBalance);
//                 console.log("USDT 代币地址:", address(usdtToken));

//                 if (usdtBalance > 0) {
//                     console.log("✅ 合约持有 USDT");
//                 } else {
//                     console.log("ℹ️  合约暂无 USDT 余额");
//                 }
//             } else {
//                 console.log("⚠️  USDT 代币地址未设置");
//             }
//         } catch {
//             console.log("❌ 无法获取 USDT 代币信息");
//         }

//         // 检查总平台费用
//         try distributionPool.totalPlatformFee() returns (uint256 totalFee) {
//             console.log("总平台费用:", totalFee);

//             if (totalFee > 0) {
//                 console.log("✅ 存在平台费用");
//             } else {
//                 console.log("ℹ️  暂无平台费用");
//             }
//         } catch {
//             console.log("❌ 无法获取总平台费用");
//         }
//     }

//     /**
//      * @notice 生成迁移状态报告
//      */
//     function generateReport() external view {
//         console.log("=== 迁移状态报告 ===");

//         // 基本信息
//         console.log("报告生成时间:", block.timestamp);
//         console.log("区块高度:", block.number);

//         // 合约版本信息
//         try distributionPool.currentVersion() returns (uint256 version) {
//             console.log("DistributionPool 版本:", version);
//         } catch {
//             console.log("DistributionPool 版本: 无法获取");
//         }

//         // 迁移完成度评估
//         uint256 migrationScore = _calculateMigrationScore();
//         console.log("迁移完成度评分:", migrationScore, "/ 100");

//         if (migrationScore >= 90) {
//             console.log("🎉 迁移状态: 优秀");
//         } else if (migrationScore >= 70) {
//             console.log("✅ 迁移状态: 良好");
//         } else if (migrationScore >= 50) {
//             console.log("⚠️  迁移状态: 需要关注");
//         } else {
//             console.log("❌ 迁移状态: 存在问题");
//         }
//     }

//     /**
//      * @notice 计算迁移完成度评分
//      */
//     function _calculateMigrationScore() internal view returns (uint256) {
//         uint256 score = 0;

//         // 版本检查 (30分)
//         try distributionPool.currentVersion() returns (uint256 version) {
//             if (version >= 2_025_081_901) {
//                 score += 30;
//             }
//         } catch {}

//         // 参数检查 (30分)
//         try distributionPool.bondingCurveTradeCap() returns (uint256 tradeCap) {
//             if (tradeCap == 400_000_000 * 1e18) {
//                 score += 15;
//             }
//         } catch {}

//         try distributionPool.minimumUsdtToBuy() returns (uint256 minUsdt) {
//             if (minUsdt == 1 * 1e3) {
//                 score += 15;
//             }
//         } catch {}

//         // USDT 设置检查 (20分)
//         try distributionPool.usdtToken() returns (IERC20 usdtToken) {
//             if (address(usdtToken) != address(0)) {
//                 score += 20;
//             }
//         } catch {}

//         // 合约访问检查 (20分)
//         try factory.owner() returns (address) {
//             score += 10;
//         } catch {}

//         try distributionPool.currentVersion() returns (uint256) {
//             score += 10;
//         } catch {}

//         return score;
//     }
// }
