# ComprehensiveMigrationV3 环境变量配置模板
# 复制此文件为 .env.migration 并填入实际值

# ==================== 必需配置 ====================

# 网络管理员私钥 (具有 ADMIN_ROLE 权限的账户)
NETWORK_ADMIN_PRIVATE_KEY="0x..."

# Factory 代理合约地址
FACTORY_PROXY_ADDRESS="0x..."

# DistributionPool 代理合约地址  
POOL_PROXY_ADDRESS="0x..."

# RPC 节点 URL
RPC_URL="https://..."

# ==================== 可选配置 ====================

# 需要迁移的币种地址 (最多支持10个)
# 如果不需要迁移特定币种，可以注释掉这些行

# COIN_TO_MIGRATE_1="0x..."
# COIN_TO_MIGRATE_2="0x..."
# COIN_TO_MIGRATE_3="0x..."
# COIN_TO_MIGRATE_4="0x..."
# COIN_TO_MIGRATE_5="0x..."
# COIN_TO_MIGRATE_6="0x..."
# COIN_TO_MIGRATE_7="0x..."
# COIN_TO_MIGRATE_8="0x..."
# COIN_TO_MIGRATE_9="0x..."
# COIN_TO_MIGRATE_10="0x..."

# ==================== 网络特定配置 ====================

# BSC 主网配置示例
# RPC_URL="https://bsc-dataseed1.binance.org/"
# FACTORY_PROXY_ADDRESS="0x..."
# POOL_PROXY_ADDRESS="0x..."

# BSC 测试网配置示例  
# RPC_URL="https://data-seed-prebsc-1-s1.binance.org:8545/"
# FACTORY_PROXY_ADDRESS="0x..."
# POOL_PROXY_ADDRESS="0x..."

# Ethereum 主网配置示例
# RPC_URL="https://mainnet.infura.io/v3/YOUR_PROJECT_ID"
# FACTORY_PROXY_ADDRESS="0x..."
# POOL_PROXY_ADDRESS="0x..."

# ==================== 使用说明 ====================

# 1. 复制此文件为 .env.migration:
#    cp script/.env.migration.template script/.env.migration

# 2. 编辑 .env.migration 文件，填入实际的配置值

# 3. 加载环境变量:
#    source script/.env.migration

# 4. 运行迁移脚本:
#    ./script/run_comprehensive_migration.sh full

# ==================== 安全提醒 ====================

# ⚠️  重要安全提醒:
# 1. 不要将包含真实私钥的 .env 文件提交到版本控制系统
# 2. 确保私钥对应的账户具有足够的权限和 Gas 费用
# 3. 在主网执行前，建议先在测试网进行完整测试
# 4. 备份重要的合约状态数据
# 5. 确认所有配置地址的正确性

# ==================== 故障排除 ====================

# 如果遇到问题，请检查:
# 1. 网络连接是否正常
# 2. RPC 节点是否可访问
# 3. 私钥对应的账户是否有足够的权限
# 4. 合约地址是否正确
# 5. Gas 费用是否充足

# 获取帮助:
# - 查看 README_ComprehensiveMigrationV3.md 文档
# - 运行验证脚本检查状态
# - 查看合约事件日志
