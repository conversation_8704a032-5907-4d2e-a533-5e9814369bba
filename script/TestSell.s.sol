// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import {DistributionPool} from "../src/DistributionPool.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract TestSell is Script {
    function setUp() public {}

    function run() public {
        // 从环境变量读取配置
        uint256 deployerPrivateKey = vm.envUint("NETWORK_ADMIN_PRIVATE_KEY");
        address poolProxyAddress = vm.envAddress("POOL_PROXY_ADDRESS");

        // 交易参数 - 卖出参数
        address coinAddress = 0x35E149718098AcF3eA5323a8CA7a24Fe34B0131a;
        uint256 tokenAmount = 351_590_653_393_740_662_791_838_869;
        // uint256 tokenAmount = 351_590_657_993_740_662_791_838_869;
        uint256 minPaymentTokenToReturn = 1; // 最小获得的 USDT 数量

        console.log("=== Test Sell Transaction ===");
        console.log("Pool Proxy Address:", poolProxyAddress);
        console.log("Coin Address:", coinAddress);
        console.log("Token Amount to Sell:", tokenAmount);
        console.log("Min Payment Token To Return:", minPaymentTokenToReturn);

        vm.startBroadcast(deployerPrivateKey);

        // 获取 DistributionPool 合约实例
        DistributionPool pool = DistributionPool(payable(poolProxyAddress));

        // 预估卖出量查询
        uint256 usdtAmount = pool.calculateUsdtAmount(coinAddress, tokenAmount);
        (uint256 usdtToReturn, uint256 sellerFee) = pool.calcUsdtAmountSell(coinAddress, tokenAmount);

        console.log("Estimated USDT Amount:", usdtAmount);
        console.log("Seller Fee:", sellerFee);
        console.log("USDT To Return:", usdtToReturn);
        // 4864550003867501600716
        // 4864550000000014550000

        if (usdtAmount < minPaymentTokenToReturn) {
            console.log("Estimated USDT amount is less than minPaymentTokenToReturn. Stopping.");
            vm.stopBroadcast();
            return;
        }

        try pool.sell(coinAddress, tokenAmount, minPaymentTokenToReturn) {
            console.log("Sell transaction successful!");
        } catch Error(string memory reason) {
            console.log("Sell transaction failed with reason:", reason);
        } catch (bytes memory lowLevelData) {
            console.log("Sell transaction failed with low level error:");
            console.logBytes(lowLevelData);
        }

        vm.stopBroadcast();
    }
}
