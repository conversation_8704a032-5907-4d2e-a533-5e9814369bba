// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import {DistributionPool} from "../src/DistributionPool.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract TestMigrateCoinToUSDT is Script {
    function setUp() public {}

    function run() public {
        // Read configuration from environment variables
        uint256 deployerPrivateKey = vm.envUint("NETWORK_ADMIN_PRIVATE_KEY");
        address poolProxyAddress = vm.envAddress("POOL_PROXY_ADDRESS");

        // Test parameters - modify these as needed
        // address coinAddress = vm.envAddress("COIN_ADDRESS"); // The IP coin address to migrate
        // uint256 minUsdtOut = vm.envUint("MIN_USDT_OUT"); // Minimum USDT to receive (with slippage protection)
        // uint256 deadline = vm.envUint("DEADLINE"); // Deadline timestamp

        address coinAddress = 0xb10048812365671B6CFa16f5eFD492fc10fe4448; // The IP coin address to migrate test-group-282
        uint256 minUsdtOut = 0; // Minimum USDT to receive (with slippage protection)
        uint256 deadline = block.timestamp + 300; // Deadline timestamp

        console.log("=== Test Migrate Coin To USDT Transaction ===");
        console.log("Pool Proxy Address:", poolProxyAddress);
        console.log("Coin Address:", coinAddress);
        console.log("Min USDT Out:", minUsdtOut);
        console.log("Deadline:", deadline);

        vm.startBroadcast(deployerPrivateKey);

        // Get DistributionPool contract instance
        DistributionPool pool = DistributionPool(payable(poolProxyAddress));

        // Pre-migration checks
        console.log("=== Pre-Migration Checks ===");

        // Check if caller has admin role
        bytes32 adminRole = pool.ADMIN_ROLE();
        address caller = vm.addr(deployerPrivateKey);
        bool hasAdminRole = pool.hasRole(adminRole, caller);
        console.log("Caller Address:", caller);
        console.log("Has Admin Role:", hasAdminRole);

        if (!hasAdminRole) {
            console.log("ERROR: Caller does not have ADMIN_ROLE. Migration will fail.");
            vm.stopBroadcast();
            return;
        }

        // Check if coin exists
        try pool.coinExists(coinAddress) returns (bool exists) {
            console.log("Coin Exists:", exists);
            if (!exists) {
                console.log("ERROR: Coin does not exist in the pool.");
                vm.stopBroadcast();
                return;
            }
        } catch {
            console.log("WARNING: Could not check if coin exists");
        }

        // Check if coin is already migrated
        try pool.isCoinMigratedToUSDT(coinAddress) returns (bool migrated) {
            console.log("Already Migrated:", migrated);
            if (migrated) {
                console.log("WARNING: Coin is already migrated to USDT.");
            }
        } catch {
            console.log("WARNING: Could not check migration status");
        }

        // Check contract ETH balance
        uint256 contractBalance = address(pool).balance;
        console.log("Contract ETH Balance:", contractBalance, "wei");
        console.log("Contract ETH Balance (ETH):", contractBalance / 1e18);

        // Get coin pool data
        try pool.getCoinPoolBasicData(coinAddress) returns (
            address ipCoinContract, address creatorNftContract, bool allowTrade
        ) {
            console.log("IP Coin Contract:", ipCoinContract);
            console.log("Creator NFT Contract:", creatorNftContract);
            console.log("Allow Trade:", allowTrade);
        } catch {
            console.log("WARNING: Could not retrieve coin pool basic data");
        }

        // Check deadline validity
        if (deadline <= block.timestamp) {
            console.log("ERROR: Deadline has already passed. Current timestamp:", block.timestamp);
            vm.stopBroadcast();
            return;
        }

        console.log("Current Block Timestamp:", block.timestamp);
        console.log("Time Until Deadline:", deadline - block.timestamp, "seconds");

        // Attempt to migrate coin to USDT
        console.log("=== Executing Migration ===");
        try pool.migrateCoinToUSDT(coinAddress, minUsdtOut, deadline) {
            console.log("Successfully migrated coin to USDT!");

            // Check migration status after
            console.log("=== Post-Migration Status ===");
            try pool.isCoinMigratedToUSDT(coinAddress) returns (bool migrated) {
                console.log("Migration Status:", migrated);
            } catch {
                console.log("WARNING: Could not check post-migration status");
            }

            // Check contract ETH balance after migration
            uint256 newContractBalance = address(pool).balance;
            console.log("New Contract ETH Balance:", newContractBalance, "wei");
            console.log("ETH Used for Swap:", contractBalance - newContractBalance, "wei");

            // Check USDT balance if possible
            try pool.usdtToken() returns (IERC20 usdtToken) {
                uint256 usdtBalance = usdtToken.balanceOf(address(pool));
                console.log("Contract USDT Balance:", usdtBalance);
            } catch {
                console.log("WARNING: Could not retrieve USDT balance");
            }
        } catch Error(string memory reason) {
            console.log("Migration failed with reason:", reason);

            // Handle specific error cases
            if (keccak256(bytes(reason)) == keccak256(bytes("CoinAlreadyMigrated()"))) {
                console.log("INFO: Coin is already migrated to USDT");
            } else if (keccak256(bytes(reason)) == keccak256(bytes("CoinNotInBondingCurve()"))) {
                console.log("INFO: Coin does not exist or is not in bonding curve");
            } else if (keccak256(bytes(reason)) == keccak256(bytes("InsufficientNativeBalance()"))) {
                console.log("INFO: Contract does not have enough ETH balance for swap");
            } else if (keccak256(bytes(reason)) == keccak256(bytes("SwapFailed()"))) {
                console.log("INFO: Uniswap swap failed - check minUsdtOut and deadline parameters");
            }
        } catch (bytes memory lowLevelData) {
            console.log("Migration failed with low level error:");
            console.logBytes(lowLevelData);
        }

        vm.stopBroadcast();

        console.log("=== Migration Test Completed ===");
        console.log("=== Usage Instructions ===");
        console.log("To run this script, set the following environment variables:");
        console.log("export NETWORK_ADMIN_PRIVATE_KEY=\"your_admin_private_key\"");
        console.log("export POOL_PROXY_ADDRESS=\"pool_contract_address\"");
        console.log("export COIN_ADDRESS=\"ip_coin_address_to_migrate\"");
        console.log("export MIN_USDT_OUT=\"minimum_usdt_amount\" # e.g., 1 for testing");
        console.log("export DEADLINE=\"deadline_timestamp\" # e.g., $(($(date +%s) + 1200))");
        console.log("");
        console.log("Then run:");
        console.log("forge script script/TestMigrateCoinToUSDT.s.sol --rpc-url $RPC_URL --broadcast");
    }
}
