// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import "@openzeppelin/contracts/proxy/transparent/ProxyAdmin.sol";
import "@openzeppelin/contracts/proxy/utils/UUPSUpgradeable.sol";
import {Factory} from "../src/Factory.sol";

contract UpgradeFactory is Script {
    function setUp() public {}

    function run() public {
        uint256 deployerPrivateKey = vm.envUint("DEPLOYER_KEY");
        address proxyAddress = vm.envAddress("FACTORY_PROXY_ADDRESS");

        vm.startBroadcast(deployerPrivateKey);

        Factory newImplementation = new Factory();
        console.log("New implementation deployed at:", address(newImplementation));

        UUPSUpgradeable proxy = UUPSUpgradeable(proxyAddress);
        proxy.upgradeToAndCall(address(newImplementation), "");

        console.log("Proxy upgraded to new implementation");

        console.log("Factory upgraded to new implementation");

        vm.stopBroadcast();
    }
}
