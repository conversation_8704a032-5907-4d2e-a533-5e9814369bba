// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

/**
 * @title PrecisionLib
 * @notice Library for handling precision conversions between USDT (18 decimals) and tokens (18 decimals)
 * @dev Provides unified functions for safe precision handling in bonding curve calculations
 */
library PrecisionLib {
    // ============ Constants ============

    /// @notice USDT has 18 decimal places (updated from 6 to support BNB chain USDT)
    uint256 public constant USDT_DECIMALS = 18;

    /// @notice Standard token decimals (18)
    uint256 public constant TOKEN_DECIMALS = 18;

    /// @notice USDT precision factor (10^18)
    uint256 public constant USDT_PRECISION = 10 ** USDT_DECIMALS;

    /// @notice Token precision factor (10^18)
    uint256 public constant TOKEN_PRECISION = 10 ** TOKEN_DECIMALS;

    /// @notice WAD precision (10^18) - used by FixedPointMathLib
    uint256 public constant WAD = 10 ** 18;

    /// @notice Scaling factor from USDT to TOKEN precision (1, since both are 18 decimals)
    uint256 public constant USDT_TO_TOKEN_SCALE = TOKEN_PRECISION / USDT_PRECISION;

    /// @notice Square root precision for bonding curve calculations (10^9)
    uint256 public constant SQRT_PRECISION = 10 ** 9;

    /// @notice Basis points precision (10,000 = 100%)
    uint256 public constant BASIS_POINTS = 10_000;

    // ============ Precision Conversion Functions ============

    /**
     * @notice Convert USDT amount (18 decimals) to 18 decimal precision
     * @dev Since both USDT and tokens use 18 decimals, this is now a pass-through function
     * @param usdtAmount Amount in USDT precision (18 decimals)
     * @return Amount in 18 decimals (same as input)
     */
    function scaleUsdtToToken(
        uint256 usdtAmount
    ) internal pure returns (uint256) {
        return usdtAmount; // No scaling needed since both are 18 decimals
    }

    /**
     * @notice Convert 18 decimal amount back to USDT precision (18 decimals)
     * @dev Since both USDT and tokens use 18 decimals, this is now a pass-through function
     * @param tokenAmount Amount in 18 decimal precision
     * @return Amount in USDT precision (18 decimals, same as input)
     */
    function scaleTokenToUsdt(
        uint256 tokenAmount
    ) internal pure returns (uint256) {
        return tokenAmount; // No scaling needed since both are 18 decimals
    }

    /**
     * @notice Convert square root result (9 decimals) to 18 decimal precision
     * @param sqrtAmount Amount in square root precision (9 decimals)
     * @return Amount scaled to 18 decimals
     */
    function scaleSqrtToToken(
        uint256 sqrtAmount
    ) internal pure returns (uint256) {
        return sqrtAmount * SQRT_PRECISION;
    }

    /**
     * @notice Convert 18 decimal amount to square root precision (9 decimals)
     * @param tokenAmount Amount in 18 decimal precision
     * @return Amount scaled to square root precision (9 decimals)
     */
    function scaleTokenToSqrt(
        uint256 tokenAmount
    ) internal pure returns (uint256) {
        return tokenAmount / SQRT_PRECISION;
    }

    // ============ Fee Calculation Functions ============

    /**
     * @notice Calculate fee using basis points for USDT amounts
     * @dev Precise calculation for USDT amounts
     * @param amount USDT amount (18 decimals)
     * @param feeRateBasisPoints Fee rate in basis points (e.g., 300 = 3%)
     * @return Fee amount in USDT precision (18 decimals)
     */
    function calculateFeeInBasisPoints(uint256 amount, uint256 feeRateBasisPoints) internal pure returns (uint256) {
        return (amount * feeRateBasisPoints) / BASIS_POINTS;
    }

    /**
     * @notice Convert WAD-based fee rate to basis points
     * @dev Converts from 18-decimal percentage to basis points
     * @param wadFeeRate Fee rate in WAD format (e.g., 3e16 = 3%)
     * @return Fee rate in basis points (e.g., 300 = 3%)
     */
    function wadToBasisPoints(
        uint256 wadFeeRate
    ) internal pure returns (uint256) {
        return (wadFeeRate * BASIS_POINTS) / WAD;
    }

    /**
     * @notice Calculate fee for USDT amounts using WAD rate but with proper precision handling
     * @dev Ensures no precision loss for USDT amounts
     * @param usdtAmount USDT amount (18 decimals)
     * @param wadFeeRate Fee rate in WAD format (e.g., 3e16 = 3%)
     * @return Fee amount in USDT precision (18 decimals)
     */
    function calculateUsdtFeeFromWad(uint256 usdtAmount, uint256 wadFeeRate) internal pure returns (uint256) {
        // Convert to basis points first to avoid precision loss
        uint256 basisPointsRate = wadToBasisPoints(wadFeeRate);
        return calculateFeeInBasisPoints(usdtAmount, basisPointsRate);
    }

    // ============ Validation Functions ============

    /**
     * @notice Validate that an amount is properly scaled for USDT (18 decimals)
     * @param amount Amount to validate
     * @return True if amount appears to be in USDT precision
     */
    function isValidUsdtAmount(
        uint256 amount
    ) internal pure returns (bool) {
        // Check if amount is reasonable for USDT (basic sanity check)
        return amount >= SQRT_PRECISION || amount == 0;
    }

    /**
     * @notice Validate that an amount is properly scaled for tokens (18 decimals)
     * @param amount Amount to validate
     * @return True if amount appears to be in token precision
     */
    function isValidTokenAmount(
        uint256 amount
    ) internal pure returns (bool) {
        // Basic sanity check - amount should be divisible by some reasonable factor
        return amount >= SQRT_PRECISION || amount == 0;
    }

    // ============ Safe Math Functions ============

    /**
     * @notice Safely multiply two amounts and scale the result
     * @dev Prevents overflow in intermediate calculations
     * @param a First amount
     * @param b Second amount
     * @param scaleDown Scale factor to divide by
     * @return Result of (a * b) / scaleDown
     */
    function safeMulDiv(uint256 a, uint256 b, uint256 scaleDown) internal pure returns (uint256) {
        require(scaleDown > 0, "PrecisionLib: scale factor cannot be zero");

        // Check for overflow in multiplication
        if (a == 0 || b == 0) return 0;
        require(a <= type(uint256).max / b, "PrecisionLib: multiplication overflow");

        return (a * b) / scaleDown;
    }
}
